<style>
/* 整体字体大小调整 */
body {
    font-size: 12px;
    line-height: 1.4;
}

/* 代码块字体大小 */
code {
    font-size: 10px;
}

pre code {
    font-size: 9px;
    line-height: 1.2;
}

/* 标题居中样式 */
.title-section {
    text-align: center;
    margin-bottom: 30px;
}

.title-section h1 {
    font-size: 18px;
    margin-bottom: 20px;
}

.title-section p {
    margin: 5px 0;
    font-size: 12px;
}

/* 目录样式 */
.toc {
    font-size: 10px;
    line-height: 1.3;
    margin: 20px 0;
}

.toc h2 {
    font-size: 12px;
    margin-bottom: 10px;
}

/* 其他标题大小调整 */
h1 { font-size: 16px; }
h2 { font-size: 14px; }
h3 { font-size: 13px; }
h4 { font-size: 12px; }
</style>

<div class="title-section">

# 项目申请书

**项目名称：** 支持 AIBrix 的 LLM 请求会话追踪功能

**项目编号：** 251a60573

**项目主导师：** 徐乐 <<EMAIL>>

**申请人：** 路昊东

**日期：** 2025.06.14

**邮箱：** <EMAIL>

**预期完成时间：** 250小时（12周）

</div>

---

<div class="toc">

## 目录

**1. 项目背景**

- 1.1 项目基本需求
  - 1.1.1 背景分析
  - 1.1.2 技术目标
- 1.2 项目相关仓库

**2. 技术方法及可行性**

- 2.1 Go语言相关技术
  - 2.1.1 AIBrix核心调度层改造
  - 2.1.2 高性能并发处理
- 2.2 Python语言相关技术
  - 2.2.1 构建客户端与测试场景
  - 2.2.2 容器化测试应用
- 2.3 Kubernetes集成可行性
- 2.4 相关调度技术分析
  - 2.4.1 Autellix方案分析与改进方向
  - 2.4.2 Parrot系统优势借鉴

**3. 项目实现细节梳理**

- 3.1 混合式会话追踪架构设计
  - 3.1.1 会话与工作流定义
  - 3.1.2 会话管理器
  - 3.1.2 进程表实现
- 3.2 两级调度算法
  - 3.2.1 工作流调度器
  - 3.2.2 请求级执行器
- 3.3 与调度适配的智能KV Cache策略
  - 3.3.1 Parrot风格的System Prompt缓存
  - 3.3.2 针对Agent工作流的流水线策略

**4. 开发时间计划（12周）**

**6. 技术创新价值**

**7. 风险评估与缓解**

</div>

---

## 1. 项目背景

### 1.1 项目基本需求

**issue仓库地址：** [https://github.com/vllm-project/aibrix/issues/633](https://github.com/vllm-project/aibrix/issues/633)

#### 1.1.1 背景分析

当前AIBrix作为vLLM的控制面，主要专注于单一LLM请求的优化处理。然而，随着LLM应用向复杂智能体（Agent）工作流发展，现有系统面临显著挑战：缺乏应用级上下文感知能力，导致队首阻塞（Head-of-Line Blocking）问题严重影响整体性能。现代LLM应用通常涉及多个相互依赖的请求序列，如多Agent协作、工具调用链和复杂推理任务，这些场景下传统的独立请求处理模式效率低下。

#### 1.1.2 技术目标

本项目需要设计一个接口来整合应用级信息，引入会话（session）和进程表（process table）的概念，保存所有应用程序及其对应任务的元数据；同时实现程序感知调度方法，该方法考虑整个应用程序的结构和依赖关系，实现更高效的执行。还要关注资源调配的优化，实现相应的会话级追踪与调度优化组件。核心目标包括：

* **降低端到端延迟** ：通过工作流感知调度减少累积等待时间
* **提升系统吞吐量** ：实现智能负载均衡和资源管理，提高GPU利用率
* **增强缓存效率** ：基于会话上下文的KV缓存优化，减少重复计算
* **支持细粒度调度** ：结合请求特征与工作流依赖的自适应调度算法

### 1.2 项目相关仓库

- **AIBrix主仓库：** [https://github.com/vllm-project/aibrix](https://github.com/vllm-project/aibrix)
- **vLLM项目：** [https://github.com/vllm-project/vllm](https://github.com/vllm-project/vllm "vllm")

---

## 2. 技术方法及可行性

### 2.1 Go语言相关技术

Go语言作为AIBrix项目的核心开发语言之一，为实现高效、可靠的会话追踪与调度提供了坚实基础。

#### 2.1.1 AIBrix核心调度层改造

本项目将利用Go语言对AIBrix的核心模块进行深度改造，以支持应用感知的会话管理：

* **实现会话与进程表：** 使用Go的高效并发数据结构（如 `sync.Map`）构建全局的会话（Session）和进程表（Process Table），用于实时存储和管理所有应用任务的元数据。
* **开发会话管理API：** 基于Go标准库的 `net/http` 包，构建 RESTful API，用于会话的创建、更新、查询和删除，为上层应用提供交互接口。
* **修改调度器逻辑：** 在现有调度器中融入程序感知能力，使其能够查询进程表，并根据任务依赖和优先级进行智能调度。

#### 2.1.2 高性能并发处理

Go语言原生的并发模型是本项目成功的关键，它能确保在引入复杂会话逻辑后系统依然保持高性能：

* **轻量级并发：** 为每个进入系统的请求（无论是API调用还是LLM推理）启动 Goroutine，轻松处理大量并发会话，避免因单个任务阻塞影响整个系统。
* **状态同步：** 利用 Channel 机制安全地在不同 Goroutine 之间同步会话状态和调度决策，避免数据竞争。
* **高效网络服务：** Go 原生支持的高性能 HTTP 服务器，能够有效承载来自大量客户端的会话管理和推理请求。

### 2.2 Python语言相关技术

Python 将作为项目主要的客户端实现和测试验证语言，充分利用其在AI领域的生态优势。

#### 2.2.1 构建客户端与测试场景

我们将使用 Python 来模拟真实的、复杂的 AI Agent 应用，以验证会话追踪功能的有效性：

* **模拟多步工作流：** 利用 Python 脚本创建模拟的自主程序，该程序会按顺序或并行地发起多个关联的LLM推理请求。
* **调用会话API：** 通过  `requests` 或 ` httpx` 库与 AIBrix 的会话管理 API 进行交互，实现会话的创建、任务注册以及最终的关闭。
* **性能基准测试：** 编写测试脚本，对比启用会话追踪前后，在不同负载下端到端延迟和系统吞吐量的变化，量化项目成果。

#### 2.2.2 容器化测试应用

为了在类生产环境中进行可靠测试，Python 测试客户端将被容器化：

* **构建Docker镜像：** 编写 `Dockerfile` 将 Python 测试应用及其依赖打包成标准化的 Docker 镜像。
* **部署至Kubernetes：** 将该镜像作为 `Deployment` 部署到 Kubernetes 集群中，与 AIBrix 服务在同一网络环境下进行交互，模拟真实微服务通信场景。

### 2.3 Kubernetes集成可行性

AIBrix 作为云原生应用，其与 Kubernetes 的深度集成为本项目的成功实施提供了保障。新的会话追踪功能将进一步利用和增强这种集成：

* **服务发现与通信：** Kubernetes 原生的Service机制确保了测试客户端（Pods）可以稳定地发现并访问AIBrix服务，这是所有交互的基础。
* **资源管理与隔离：** Kubernetes 负责提供底层的计算资源（CPU/GPU/内存）隔离与分配。本项目的程序感知调度是在此基础上进行的更精细化的应用层调度，二者相辅相成，共同提升资源利用率。
* **可观测性基础：** Kubernetes 提供了强大的监控和日志收集框架。本项目新增的会话ID可以作为关键的 `label` 或 `tag` 注入到日志和监控指标（Metrics）中，极大地提升了对复杂AI应用行为的追踪和调试能力。

### 2.4 相关调度技术分析

#### 2.4.1 Autellix方案分析与改进方向

Autellix提出了程序感知调度的概念，通过 PLAS 和 ATLAS 算法实现了 4-15 倍的吞吐量提升。然而，Autellix 存在以下局限性：

- 调度粒度过粗：仅基于程序级累积服务时间，忽略了单个请求的特征差异；
- 缓存策略简单：缺乏针对 LLM 特性的 KV 缓存优化机制；
- 依赖关系处理局限：DAG分析能力有限，难以处理动态变化的工作流结构，而且其调度偏向于“被动响应”，对工作流（Workflow）内部的结构化信息利用不足，可能导致次优的抢占决策。

#### 2.4.2 Parrot系统优势借鉴

Parrot系统在 OSDI‘2024 中展示了基于语义变量的工作流调度优势：

- KV 缓存管理创新：使用系统提示匹配替代前缀匹配，大幅降低开销；
- 工作流感知调度：基于不同性能目标合理组合分配请求的智能调度，实现性能提升；
- 数据流分析能力：通过语义变量构建请求间依赖关系，支持复杂工作流优化。

但 Parrot 的调度依赖于开发者在代码中指定的性能“提示”，缺乏动态适应性。本项目将借鉴 Autellix 以及 Parrot 的核心思想，结合 AIBrix 的架构特点，设计更适合的解决方案。

---

## 3. 项目实现细节梳理

### 3.1 混合式会话追踪架构设计

**本架构设计的核心是构建一个**应用感知（Application-Aware）**的调度系统。它不仅追踪独立的请求，更将整个会话（Session）视为一个包含内在逻辑的**工作流（Workflow）**，从而实现从宏观到微观的端到端优化。**

#### 3.1.1 会话与工作流定义 (Session & Workflow Definition)

Session 是系统的基本管理单元，它封装了一个完整的用户任务。其核心是一个有向无环图（DAG），精准地描述了任务内部所有子请求（LLM调用）的依赖关系，类似下面结构：

```go
// Session 代表一个完整的、由多个关联请求组成的端到端任务。
type Session struct {
    ID          string                 // 全局唯一的会话ID
    DAG         *WorkflowDAG           // 核心：描述任务内部依赖关系的有向无环图
    Priority    int                    // 会话的优先级 (e.g., 0:高, 1:中, 2:低)，用于抢占式调度
    Status      SessionStatus          // 会话的整体状态 (Running, Waiting, Completed)
    Metadata    map[string]interface{} // 可扩展的元数据 (e.g., 用户ID, 创建时间)
    mutex       sync.RWMutex           // 保护会话内部状态的读写锁
}

// WorkflowDAG 定义了会话内部的计算图结构。
type WorkflowDAG struct {
    Nodes         map[string]*RequestNode // 图中所有的请求节点，以RequestID为键
    AdjacencyList map[string][]string       // 邻接表，描述了请求间的依赖关系 (key: 前置任务ID, value: 后续任务ID列表)
}

// RequestNode 是DAG中的一个节点，代表一个独立的计算单元（如一次LLM调用）。
type RequestNode struct {
    ID              string          // 节点唯一的ID
    Status          RequestStatus   // 节点状态 (Pending, Ready, Running, Pipelining, Completed, Failed)
    RequestData     interface{}     // 请求的具体负载 (e.g., prompt, model_params)
    ResultData      interface{}     // 节点的计算结果
    AssignedGPU     string          // 被分配执行的GPU ID，对流水线策略至关重要
    CacheKey        string          // 用于KV Cache的哈希键 (e.g., hash(system_prompt))
    ExecutionStats  ExecutionStats  // 性能统计数据 (等待时间, 执行时间等)
}
```

这里， `DAG` 字段是本架构的基石，它使得调度器能够**预知未来**，从简单的“响应式”调度进化为“预测与规划式”调度；

需要注意的是， `RequestNode` 的 `Status` 极为关键，它是我们两级调度算法决策的核心依据。`Ready` 状态的节点是工作流调度器的候选池，而 `Pipelining` 状态则能激活请求级执行器的特殊处理逻辑。

#### 3.1.2 会话管理器 (Session Manager)

会话管理器是一个相对轻量的模块，其职责清晰而专一：负责 `Session` 对象的生命周期管理，并为系统的其他部分提供一个稳定的查询接口。

```go
// SessionManager 负责所有会话的生命周期管理 (创建, 查询, 更新, 删除)。
type SessionManager struct {
    sessions map[string]*Session // 内存中存储所有活跃的会话
    mutex    sync.RWMutex        // 保护sessions map的并发访问
}

// CreateNewSession 创建一个新的会話实例，并初始化其DAG。
func (sm *SessionManager) CreateNewSession(priority int, dagInfo DagDefinition) (*Session, error) {
    // 实现创建逻辑
}

// GetSessionByID 安全地根据ID获取一个会话。
func (sm *SessionManager) GetSessionByID(sessionID string) (*Session, bool) {
    // 实现查询逻辑
}
```

我们将调度逻辑与会话的存活管理解耦。SessionManager 像一个“户籍系统”，只管登记和注销，而具体的“社会活动”则由更专业的进程表和调度器来负责。

#### 3.1.2 进程表实现

在我们的设计里，进程表可能不再是 Autellix 设计的简单复刻，而是演进为我们混合式调度模型的动态世界观。它不直接存储会话的静态结构，而是持续追踪和聚合所有活跃工作流的运行时关键指标，为第一级（工作流）调度器提供决策依据。

进程表的核心职责：

* **服务质量追踪**：监控每个会话的累积执行与等待时间，用于判断哪些工作流可能饥饿或延迟过高。
* **工作流状态视图**：取代简单的“线程元数据”，提供一个全局的、可供调度器查询的DAG状态视图，这是工作流调度器的主要输入。
* **资源局部性感知**：为我们的流水线（Pipelining）策略提供数据支持，跟踪每个工作流在物理资源上的分布。

### 3.2 两级调度算法 (Two-Level Scheduling Algorithm)

本项目将研发一种独特的混合式调度算法，旨在融合二者之长处，并针对AIBrix进行深度优化：

* **动态获取结构信息**：我们不要求开发者预先标注，而是像Autellix一样，从会话进程表（Session Process Table）中动态解析出工作流的DAG（有向无环图）结构和任务依赖。
* **高效利用结构信息**：我们借鉴Parrot的思想，利用解析出的结构信息（如共享的System Prompt、任务依赖关系）来指导我们的KV Cache管理和调度决策，实现智能的 prefetcher 组件以及更加细粒度的 pipeline，实现更高效的执行。

为兼顾工作流的宏观优化与单个请求的微观特性，调度器将采用两级设计：

#### 3.2.1 工作流调度器 (Workflow-Level Scheduler)

此调度器负责宏观决策，它审视整个会话的DAG，决定**下一步应该执行哪个或哪些请求**。这里可以借鉴 Autellix 论文的主要思想以及调度策略，核心包括：

- 动态 DAG 的解析、构建和关键路径识别
- 基于关键路径的优先级计算
- 防饥饿机制和公平性保证

大致的工作流循环调度如下面伪代码所示：

```go
function WorkflowSchedulerLoop():
    while true:
        // 1. 从会话表中获取所有就绪的请求
        ready_requests = session_table.get_ready_requests()

        // 2. 如果没有就绪请求，短暂等待
        if not ready_requests:
            sleep(TINY_INTERVAL)
            continue

        // 3. 根据混合策略对就绪请求进行评分
        scored_requests = score_requests(ready_requests, scoring_policy)

        // 4. 选择得分最高的请求
        best_request = select_best(scored_requests)

        // 5. 将选中的请求派发给第二级调度器
        RequestLevelScheduler.dispatch(best_request)
```

#### 3.2.2 请求级执行器 (Request-Level Executor)

此执行器负责微观层面的执行策略，决定一个被选中的请求**具体应该如何被执行**，基本逻辑如下：

```go
function RequestLevelExecutor.dispatch(request):
    // 1. 分析请求特性
    request_type = analyze(request) // e.g., Prefill-heavy, Decode-heavy, Pipelined

    // 2. 检查是否可应用流水线（Pipelining）
    if can_pipeline_with_dependency(request):
        // 分配到依赖任务所在的GPU，并启动流水线执行
        assign_to_gpu(request.dependency.gpu)
        execute_pipelined(request)
        return

    // 3. 检查是否可应用KV Cache
    if can_use_kv_cache(request):
        // 指示GPU加载共享的KV Cache
        prepare_gpu_with_cache(request.gpu, request.cache_key)

    // 4. 根据请求类型选择最优的执行硬件和方式
    assign_and_execute_normal(request, request_type)
```

### 3.3 与调度适配的智能KV Cache策略

我们不实现分布式KV Cache系统本身，而是研发一套与调度系统紧密适配的缓存管理与调用策略。

#### 3.3.1 Parrot风格的System Prompt缓存

我们放弃高开销的逐 Token 前缀匹配。当多个请求共享完全相同的 System Prompt 或固定的前缀时，我们将该前缀的 KV Cache 计算一次并存储。后续请求通过其 hash(system_prompt) 键直接复用，实现 O(1) 查找。

#### 3.3.2 针对Agent工作流的流水线策略 (Pipelining Strategy)

在 Agent 工作流中，常出现后一个 LLM Call 的输入依赖于前一个 LLM Call 的输出。我们可以打破“生成完毕再执行”的壁垒。当前一个请求（Req_A）在解码（Decode）时，每生成一个或一批（Chunk）Token，这些Token立刻被用于后一个依赖请求（Req_B）的预填充（Prefill）阶段。为最小化数据传输开销，调度器会尽可能将这对依赖请求调度到同一块GPU上。类似逻辑如下：

```go
function execute_pipelined(request_B):
    request_A = request_B.dependency

    // 确保在同一GPU上
    assert request_A.gpu == request_B.gpu

    // 并行启动A的解码和B的prefill准备
    start_prefill_setup(request_B)
  
    // A每生成一个块，就送给B
    for chunk in decode_stream(request_A):
        // 将A的输出块作为B的输入进行预填充
        append_to_prefill_buffer(request_B, chunk)
  
    // A解码完成后，B可能已完成大部分预填充，继续执行B
    finish_execution(request_B)
```

---

## 4. 开发时间计划（12周）

### **第一阶段：基础架构与核心设计（第1周 - 第2周）**

* **第1周：环境搭建与技术深潜,会话管理API开发**

  * **主要任务**：搭建AIBrix开发与测试环境；精读 Autellix 和 Parrot 论文，分析其设计得失；初步设计会话进程表的数据结构。使用 Go 语言实现会话创建、更新、查询的基础API框架；编写 Python 客户端原型来调用这些API。
  * **预期产出**：可运行的AIBrix环境；一份两篇论文的对比分析报告；会话进程表的初步Go语言结构定义（Structs）；以及一套可调用的 RESTful API；一个能创建和查询会话的 Python 脚本。
* **第2周：DAG解析与工作流调度器原型**

  * **主要任务**：实现从会话进程表中解析任务依赖关系并构建 DAG 的功能；开发第一级调度器原型，能够识别出“就绪”请求。
  * **预期产出**：一个能将任务列表转化为 DAG 的 Go 模块；调度器可以打印出当前所有可执行的请求列表。

### **第二阶段：核心调度与缓存策略实现（第3周 - 第9周）**

* **第3周：两级调度器联动**

  * **主要任务**：实现第一级调度器到第二级执行器的派发逻辑；开发第二级执行器的基本框架，能根据请求类型做简单分发。
  * **预期产出**：一个完整的、但策略简单的两级调度流程可以跑通。
* **第4周：实现Parrot风格的System Prompt缓存**

  * **主要任务**：实现基于哈希的 KV Cache 存储和查找逻辑；改造调度器和执行器，使其在处理请求时会检查并利用这种缓存。
  * **预期产出**：对于有相同 System Prompt 的请求，可以观察到显著的prefill加速。
* **第5-7周：实现流水线（Pipelining）调度策略**

  * **主要任务**：这是本项目最复杂的部分。修改调度器，使其能识别出可流水线的依赖请求对，并将它们调度到同一 GPU；修改模型执行后端，支持流式解码与预填充的协同工作。
  * **预期产出**：一个包含依赖请求的 Agent 工作流 demo，可以通过流水线方式执行，并观察到端到端延迟降低。
* **第8-9周：抢占与优先级**

  * **主要任务**：在调度器中加入优先级判断和抢占逻辑。当更高优先级的会话请求就绪时，能够暂停（而非终止）当前低优先级的任务。
  * **预期产出**：测试用例证明高优先级任务的等待时间显著缩短。

### **第三阶段：集成、测试与交付（第10周 - 第12周）**

* **第10周：端到端集成与性能测试, 系统调优与稳定性增强**

  * **主要任务**：将所有功能模块（调度、缓存、流水线、抢占）平滑集成；设计并执行综合性能测试（Benchmark），对比基线系统。根据性能测试结果，找出瓶颈并进行代码优化；增加边界情况的测试用例，提升系统鲁棒性。
  * **预期产出**：一个功能完整的 AIBrix 版本；比较完善的性能测试报告和图表。
* **第11周：文档撰写**

  * **主要任务**：撰写详细的设计文档、API 使用手册、以及新功能的配置和使用指南。为所有关键代码段添加清晰的注释。
  * **预期产出**：完整的项目文档。
* **第12周：代码审查与最终交付**

  * **主要任务**：整理代码，格式化并提交 Pull Request；根据社区导师的反馈进行修改，准备最终的项目成果演示。
  * **预期产出**：一个高质量、符合社区规范的 PR；最终的项目演示材料。

## 6. 技术创新价值

基于Autellix论文的研究结果，本项目预期实现多倍的程序吞吐量提升，并且通过优化KV缓存策略和数据局部性管理，大大降低系统延迟。

项目将为AIBrix和vLLM开源社区贡献重要的会话追踪和程序感知调度功能，实现的技术方案将推动LLM推理基础设施的发展，为大规模AI应用部署提供关键技术支撑。

## 7. 风险评估与缓解

考虑到Autellix算法的复杂性和分布式系统的挑战，项目采用渐进式开发方法，通过实现多种调度策略和建立完善的监控机制来降低技术风险。项目设置了明确的里程碑节点，包括各种关键交付点，采用敏捷开发模式，确保及时发现和解决开发过程中的问题。

通过系统性的技术方案设计和详细的时间规划，本项目将为AIBrix提供完整的LLM会话追踪功能，推动LLM推理系统向更高效、可扩展的方向发展。

---

<style>#mermaid-1749882467097{font-family:sans-serif;font-size:16px;fill:#333;}#mermaid-1749882467097 .error-icon{fill:#552222;}#mermaid-1749882467097 .error-text{fill:#552222;stroke:#552222;}#mermaid-1749882467097 .edge-thickness-normal{stroke-width:2px;}#mermaid-1749882467097 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1749882467097 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1749882467097 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1749882467097 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1749882467097 .marker{fill:#333333;}#mermaid-1749882467097 .marker.cross{stroke:#333333;}#mermaid-1749882467097 svg{font-family:sans-serif;font-size:16px;}#mermaid-1749882467097 .label{font-family:sans-serif;color:#333;}#mermaid-1749882467097 .label text{fill:#333;}#mermaid-1749882467097 .node rect,#mermaid-1749882467097 .node circle,#mermaid-1749882467097 .node ellipse,#mermaid-1749882467097 .node polygon,#mermaid-1749882467097 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-1749882467097 .node .label{text-align:center;}#mermaid-1749882467097 .node.clickable{cursor:pointer;}#mermaid-1749882467097 .arrowheadPath{fill:#333333;}#mermaid-1749882467097 .edgePath .path{stroke:#333333;stroke-width:1.5px;}#mermaid-1749882467097 .flowchart-link{stroke:#333333;fill:none;}#mermaid-1749882467097 .edgeLabel{background-color:#e8e8e8;text-align:center;}#mermaid-1749882467097 .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#mermaid-1749882467097 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-1749882467097 .cluster text{fill:#333;}#mermaid-1749882467097 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:sans-serif;font-size:12px;background:hsl(80,100%,96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-1749882467097:root{--mermaid-font-family:sans-serif;}#mermaid-1749882467097:root{--mermaid-alt-font-family:sans-serif;}#mermaid-1749882467097 flowchart{fill:apa;}</style>
