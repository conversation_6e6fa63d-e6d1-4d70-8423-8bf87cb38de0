<style>
/* 整体字体大小调整 */
body {
    font-size: 12px;
    line-height: 1.4;
}

/* 代码块字体大小 */
code {
    font-size: 10px;
}

pre code {
    font-size: 9px;
    line-height: 1.2;
}

/* 标题居中样式 */
.header-section {
    text-align: center;
    margin-bottom: 30px;
}

.header-section h1 {
    font-size: 18px;
    margin-bottom: 15px;
}

.header-section p {
    margin: 8px 0;
    font-size: 11px;
}

/* 标题大小调整 */
h1 { font-size: 16px; }
h2 { font-size: 14px; }
h3 { font-size: 13px; }
h4 { font-size: 12px; }

/* 列表项字体调整 */
li {
    font-size: 11px;
    line-height: 1.3;
}

/* 段落字体调整 */
p {
    font-size: 11px;
    line-height: 1.3;
}

/* 强调文本 */
strong {
    font-size: 11px;
}

/* 斜体文本 */
em {
    font-size: 10px;
}
</style>

<div class="header-section">

# 路昊东

(+852) 93416717 | <<EMAIL>> | [sleepylgod.github.io](https://sleepylgod.github.io/) | [SleepyLGod](https://github.com/SleepyLGod) (GitHub)

</div>

---

### **技术栈**

* **编程语言**: Golang (熟悉), Java (熟悉), Python (熟悉), C/C++ (熟悉), JavaScript/NodeJS (熟悉), CUDA (熟悉), Rust (了解), Solidity (了解), x86 汇编 (熟悉)
* **工程技能**: Linux (熟悉), MySQL (熟悉), TiDB (熟悉), Git (熟悉), Docker (了解), Kubernetes (了解)
* **其他**: LaTeX (熟悉), Markdown (熟悉)

---

### **开源项目经验**

* **XETH 项目 (负责人)**: 领导团队开发了一个面向以太坊数据智能分析和管理的开源平台。
* **PingCAP Talent Plan (TinyKV, TinySQL)**: 参与 PingCAP 人才计划，完成分布式键值存储项目 TinyKV 以及分布式关系型数据库 TinySQL 开发。
* **DComp (ICPP 2023 论文, 贡献者)**: 参与“DComp”项目，将 LSM-Tree 的 compaction 操作卸载到 DPU 硬件加速器，并贡献于相关论文。

---

### **科研与项目经历**

**研究助理 | OPAttention**

* *华中科技大学服务计算与系统教育部重点实验室 (武汉) | 2024.04 - 2024.07*
* 共同领导针对大语言模型中 KVCache 内存管理策略的优化研究 “OPAttention”。
* 探索对大语言模型底层KV Cache存储管理的进一步优化。

**研究助理 | GPU-based 压缩算法**

* *威斯康星大学麦迪逊分校数据库组 (美国, Hybrid) | 2023.05 - 2023.08*
* 优化 GPU 上轻量级整数压缩算法。
* 探索在 GPU 上下文中更高效的位打包压缩和优化技术。

**研究助理 | Lasagne**

* *华中科技大学服务计算与系统教育部重点实验室 (武汉) | 2022.06 - 2023.04*
* 共同领导对三明治套利策略的扩展研究，并提出“Lasagne”策略，实现一个以太坊套利系统与相应 Mini 平台。
* 通过实验评估策略，将套利速度提升1.53倍并优化了准确性。

**研究实习生 | DComp**

* *PingCAP & 武汉国家光电实验室 (武汉) | 2022.01 - 2023.04*
* 基于 RocksDB 将 LSM-tree 的 compaction 操作卸载至 DPU 硬件加速器。
* 为 "Dcomp" 项目开发了一个基于 RDMA 的轻量级客户端-服务器文件系统。
* 参与贡献 ICPP 2023 论文 "DComp: Efficient Offload of LSM-tree Compaction with Data Processing Units"。

**负责人 | KinderPanel**

- *华中科技大学 Dian 团队 ｜ 2021. 01 - 2021.05*
- 基于Java 以及 Go 为后端语言分别领导开发一个大型幼儿园儿童健康综合服务平台。

**研究助理 | 感知融合假手与移动机器人**

* *华中科技大学数字制造装备与技术国家重点实验室 (武汉) | 2020.10 - 2021.12*
* 共同发明了一种感知融合假手（已获专利），负责感知采集与反馈模块的开发。
* 参与开发用于大型飞机变曲率蒙皮的吸附式移动加工机器人系统。
* 使用 AprilTags 视觉基准系统消除累积误差，实现厘米级定位。

---

### **教育背景**

* **香港中文大学** | 2024.08 - 2028.06 (预计)
  * 计算机科学与工程博士在读
* **华中科技大学** | 2020.09 - 2024.06
  * 计算机科学与技术学士
  * GPA: 3.85 / 4.00
* **加州大学伯克利分校** | 2023.01 - 2023.06
  * 交换项目
  * GPA: 4.00 / 4.00

---

### **奖励与专利**

**荣誉奖项**

* 校优秀毕业生 (2024.07)
* 校自强奖学金 (2023.10)
* **国家级一等奖**: 第十七届“挑战杯”全国大学生课外学术科技作品竞赛 (2022.04)
* **省级特等奖**: 第十三届湖北省“挑战杯”大学生课外学术科技作品竞赛 (2021.05)
* **校级特等奖**: 第八届“求是杯”大学生课外学术科技作品竞赛 (2021.04)
* **校级一等奖**: 第十七届“迈瑞杯”大学生机器人大赛 (2021.04)
* 校科创奖学金 (2022.10)
* 校三好学生奖学金 (2021.10)
* 国家海信奖学金 (2022.05)
* 国家华为“智能基座”奖学金 (2022.05)
* 校新生卓越奖学金 (2021.04)
* 校新生自强奖学金 (2021.04)

**发明专利**

- **感知融合假手 (共同发明人)**, CN Patent CN215192638U, 授权日期: 2021.12.17
- **实现自适应柔性恒力磨削功能的磨头 (共同发明人)**, CN Patent CN214923415U, 授权日期: 2021.11.30

---
